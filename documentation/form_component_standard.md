# Standard Form Component Structure

All form components should follow this consistent pattern:

```elixir
defmodule RivaAshWeb.Components.Forms.[ComponentName] do
  @moduledoc """
  [Brief description]
  """
  use Phoenix.Component   # Standard module setup

  # Import atomic components explicitly
  import RivaAshWeb.Components.Atoms.[...]
  import RivaAshWeb.Components.Molecules.[...]
  import RivaAshWeb.Components.Interactive.[...]

  @doc """
  Renders [component purpose]
  """
  attr :form, :map, required: true
  attr :editing, :boolean, default: false
  attr :loading, :boolean, default: false
  attr :on_submit, :string, required: true
  attr :on_change, :string, required: true
  attr :on_cancel, :string, required: true
  attr :class, :string, default: ""
  # Additional component-specific attributes

  def [component_name](assigns) do
    ~H"""
    <.card variant="elevated" class={@class}>
      <:header>
        <.card_title>
          <%= if @editing, do: "Edit [Entity]", else: "Create New [Entity]" %>
        </.card_title>
      </:header>
      
      <:body>
        <.form
          for={@form}
          phx-submit={@on_submit}
          phx-change={@on_change}
          class="space-y-6"
        >
          <!-- Form fields using atomic components -->
          <.form_field ... />
          
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <.button 
              type="button" 
              variant="outline" 
              phx-click={@on_cancel} 
              disabled={@loading}
            >
              Cancel
            </.button>
            
            <.button type="submit" loading={@loading}>
              <%= if @editing, do: "Update [Entity]", else: "Create [Entity]" %>
            </.button>
          </div>
        </.form>
      </:body>
    </.card>
    """
  end
end
```

## Key Features
1. **Module Structure**: Uses `Phoenix.Component` with explicit imports
2. **Attribute Declarations**: Clear definition of all supported attributes
3. **Card Wrapper**: All forms wrapped in `.card` component
4. **Consistent Actions**: Submit/Cancel buttons in the same position and style
5. **Atomic Design**: Uses form_field, button, card and other atomic components
6. **Responsive Layout**: Grid layouts for multi-column fields
7. **State Handling**: Loading states and edit/create modes