defmodule RivaAshWeb.Components.Forms.ItemTypeForm do
  @moduledoc """
  Form for creating and editing item types.
  """
  use Phoenix.Component
  import RivaAshWeb.Components.Atoms.Button
  import RivaAshWeb.Components.Molecules.Card
  import RivaAshWeb.Components.Molecules.FormField

  attr :form, :map, required: true
  attr :editing, :boolean, default: false
  attr :loading, :boolean, default: false
  attr :on_submit, :string, required: true
  attr :on_change, :string, required: true
  attr :on_cancel, :string, required: true
  attr :class, :string, default: ""

  def item_type_form(assigns) do
    ~H"""
    <.card variant="elevated" class={@class}>
      <:header>
        <.card_title>
          <%= if @editing, do: "Edit Item Type", else: "Create New Item Type" %>
        </.card_title>
      </:header>
      <:body>
        <.form
          for={@form}
          phx-submit={@on_submit}
          phx-change={@on_change}
          class="space-y-6"
        >
          <.form_field
            field={@form[:name]}
            label="Name"
            type="text"
            required={true}
          />

          <div class="flex justify-end space-x-3 pt-4 border-t">
            <.button
              type="button"
              variant="outline"
              phx-click={@on_cancel}
              disabled={@loading}
            >
              Cancel
            </.button>
            <.button type="submit" loading={@loading}>
              <%= if @editing, do: "Update Item Type", else: "Create Item Type" %>
            </.button>
          </div>
        </.form>
      </:body>
    </.card>
    """
  end
end
